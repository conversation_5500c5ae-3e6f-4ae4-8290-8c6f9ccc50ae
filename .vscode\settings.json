{"C_Cpp.intelliSenseEngine": "default", "C_Cpp.intelliSenseEngineFallback": "enabled", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.autocomplete": "default", "C_Cpp.suggestSnippets": true, "C_Cpp.default.configurationProvider": "ms-vscode.vscode-json", "C_Cpp.default.compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32/bin/xtensa-esp32-elf-gcc.exe", "files.associations": {"*.ino": "cpp", "*.h": "c", "*.hpp": "cpp", "*.cpp": "cpp", "*.c": "c"}}