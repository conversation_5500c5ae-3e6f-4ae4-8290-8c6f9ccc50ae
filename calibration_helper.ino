/*
 * ESP32 Soil Moisture Sensor Calibration Helper
 * 
 * This program helps you calibrate your soil moisture sensor by:
 * 1. Continuously reading raw values from the sensor
 * 2. Displaying min/max values encountered
 * 3. Providing calibration values for the main program
 * 
 * Instructions:
 * 1. Upload this program to your ESP32
 * 2. Open Serial Monitor (115200 baud)
 * 3. Place sensor in completely DRY soil - note the "Dry Value"
 * 4. Place sensor in completely WET soil - note the "Wet Value"
 * 5. Use these values to update MOISTURE_DRY and MOISTURE_WET in main program
 * 
 * Pin Configuration:
 * - Soil Moisture Sensor on GPIO 18 (same as main program)
 */

#define MOISTURE_PIN 18
#define SAMPLES_PER_READING 10
#define READING_INTERVAL 1000  // 1 second between readings

int minValue = 4095;  // Start with maximum possible value
int maxValue = 0;     // Start with minimum possible value
int readingCount = 0;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  pinMode(MOISTURE_PIN, INPUT);
  
  Serial.println("╔═══════════════════════════════════════════════════════════╗");
  Serial.println("║        ESP32 Soil Moisture Sensor Calibration Helper     ║");
  Serial.println("╠═══════════════════════════════════════════════════════════╣");
  Serial.println("║                                                           ║");
  Serial.println("║  Instructions:                                            ║");
  Serial.println("║  1. Place sensor in COMPLETELY DRY soil                  ║");
  Serial.println("║  2. Wait for readings to stabilize                       ║");
  Serial.println("║  3. Note the 'Dry Value' shown below                     ║");
  Serial.println("║  4. Place sensor in COMPLETELY WET soil                  ║");
  Serial.println("║  5. Wait for readings to stabilize                       ║");
  Serial.println("║  6. Note the 'Wet Value' shown below                     ║");
  Serial.println("║  7. Use these values in your main program                ║");
  Serial.println("║                                                           ║");
  Serial.println("╚═══════════════════════════════════════════════════════════╝");
  Serial.println();
  
  Serial.println("Starting calibration readings...");
  Serial.println("┌──────┬─────────┬───────────┬───────────┬─────────────────┐");
  Serial.println("│ #    │ Current │ Dry Value │ Wet Value │ Recommendation  │");
  Serial.println("│      │ Reading │ (Min)     │ (Max)     │                 │");
  Serial.println("├──────┼─────────┼───────────┼───────────┼─────────────────┤");
}

void loop() {
  delay(READING_INTERVAL);
  
  readingCount++;
  
  // Read sensor with averaging
  int currentReading = readSensorAverage();
  
  // Update min/max values
  if (currentReading < minValue) {
    minValue = currentReading;
  }
  if (currentReading > maxValue) {
    maxValue = currentReading;
  }
  
  // Display current reading and calibration values
  displayReading(currentReading);
  
  // Every 20 readings, show calibration code
  if (readingCount % 20 == 0) {
    showCalibrationCode();
  }
}

int readSensorAverage() {
  long sum = 0;
  int validReadings = 0;
  
  for (int i = 0; i < SAMPLES_PER_READING; i++) {
    int reading = analogRead(MOISTURE_PIN);
    if (reading >= 0 && reading <= 4095) {
      sum += reading;
      validReadings++;
    }
    delay(10);
  }
  
  if (validReadings == 0) {
    return -1;
  }
  
  return sum / validReadings;
}

void displayReading(int current) {
  Serial.print("│ ");
  Serial.printf("%4d", readingCount);
  Serial.print(" │ ");
  
  if (current >= 0) {
    Serial.printf("%7d", current);
  } else {
    Serial.print("  ERROR");
  }
  Serial.print(" │ ");
  
  Serial.printf("%9d", minValue);
  Serial.print(" │ ");
  
  Serial.printf("%9d", maxValue);
  Serial.print(" │ ");
  
  // Provide recommendation
  String recommendation = getRecommendation(current);
  Serial.print(recommendation);
  
  // Pad the recommendation field
  for (int i = recommendation.length(); i < 15; i++) {
    Serial.print(" ");
  }
  Serial.println(" │");
}

String getRecommendation(int current) {
  int range = maxValue - minValue;
  
  if (range < 100) {
    return "Need more range";
  } else if (current == minValue) {
    return "At DRY value";
  } else if (current == maxValue) {
    return "At WET value";
  } else if (abs(current - minValue) < 50) {
    return "Near DRY";
  } else if (abs(current - maxValue) < 50) {
    return "Near WET";
  } else {
    return "Intermediate";
  }
}

void showCalibrationCode() {
  Serial.println("├──────┴─────────┴───────────┴───────────┴─────────────────┤");
  Serial.println("│                 CALIBRATION VALUES                       │");
  Serial.println("├───────────────────────────────────────────────────────────┤");
  Serial.print("│ #define MOISTURE_DRY ");
  Serial.printf("%4d", minValue);
  Serial.println("   // Dry soil value        │");
  Serial.print("│ #define MOISTURE_WET ");
  Serial.printf("%4d", maxValue);
  Serial.println("   // Wet soil value        │");
  Serial.println("│                                                           │");
  
  // Calculate suggested threshold (30% of range from dry end)
  int suggestedThreshold = minValue + (maxValue - minValue) * 0.3;
  Serial.print("│ Suggested irrigation threshold: ");
  Serial.printf("%4d", suggestedThreshold);
  Serial.println(" (30% moisture)    │");
  Serial.println("├──────┬─────────┬───────────┬───────────┬─────────────────┤");
}
