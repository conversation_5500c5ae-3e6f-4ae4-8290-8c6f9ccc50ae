#ifndef SDKCONFIG_H
#define SDKCONFIG_H

// ESP32 SDK Configuration for IntelliSense
// This file provides basic ESP32 configuration definitions for IDE support
// The actual sdkconfig.h is generated by the ESP-IDF build system

// Basic ESP32 configuration
#define CONFIG_ESP32_DEFAULT_CPU_FREQ_240 1
#define CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ 240

// FreeRTOS configuration
#define CONFIG_FREERTOS_HZ 1000
#define CONFIG_FREERTOS_MAX_TASK_NAME_LEN 16

// Arduino configuration
#define CONFIG_ARDUINO_LOOP_STACK_SIZE 8192
#define CONFIG_ARDUINO_EVENT_RUNNING_CORE 1

// Hardware abstraction layer
#define CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE 1

// Debug configuration
#define CONFIG_LOG_DEFAULT_LEVEL 3

// WiFi configuration (if needed in future)
#define CONFIG_ESP32_WIFI_ENABLED 1

// Bluetooth configuration (if needed in future)
#define CONFIG_BT_ENABLED 1

// Flash configuration
#define CONFIG_ESPTOOLPY_FLASHSIZE_4MB 1
#define CONFIG_ESPTOOLPY_FLASHSIZE "4MB"

// Memory configuration
#define CONFIG_ESP32_SPIRAM_SUPPORT 1

// GPIO configuration
#define CONFIG_GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL 1

#endif // SDKCONFIG_H
